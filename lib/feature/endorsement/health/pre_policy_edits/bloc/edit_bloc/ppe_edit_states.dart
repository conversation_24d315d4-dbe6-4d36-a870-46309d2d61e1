import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/models/ui_models/ppe_form_editing_models.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/models/ui_models/ppe_policy_changes_model.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/models/validation_models/family_constrains.dart';
import 'package:acko_flutter/network/ApiResponse.dart';

abstract class PPEEditState {}

class Initial extends PPEEditState {}

class Loading extends PPEEditState {}

class AutoScroll extends PPEEditState {
  bool scrollToBottom;
  AutoScroll({this.scrollToBottom = false});
}

class ShowFormEditingLoader extends PPEEditState {
  bool fullPageLoader;
  ShowFormEditingLoader({this.fullPageLoader = false});
}

class DismissFormEditingLoader extends PPEEditState {
  bool fullPageLoader;
  DismissFormEditingLoader({this.fullPageLoader = false});
}

class Loaded extends PPEEditState {
  FormValues? formValues;
  bool addMemberFlow;
  bool redirectToNextNode;
  int? amount;
  int totalEdits;
  bool enableCta;
  bool? isFooterLoading;
  EndorsementFormValidatorConfig validationConfig;
  bool refreshOverview;
  bool showAddMore;
  Loaded(
      {this.formValues,
      this.addMemberFlow = false,
      this.redirectToNextNode = false,
      required this.totalEdits,
      this.amount,
      this.enableCta = false,
      this.isFooterLoading,
      required this.validationConfig,
      this.refreshOverview = false,
      this.showAddMore = true});
}

class Error extends PPEEditState {
  ErrorHandler? error;
  Error({this.error});
}

class ErrorToast extends PPEEditState {}

class MinimumMemberRequiredToast extends PPEEditState {}

class ReviewSheetLoading extends PPEEditState {}

class ReviewSheetLoaded extends PPEEditState {
  PolicyChangesData policyChangesData;
  int totalEdits;
  ReviewSheetLoaded(
      {required this.policyChangesData, required this.totalEdits});
}

class ReviewSheetError extends PPEEditState {
  // ErrorHandler? error;
  // ReviewSheetError({this.error});
}

class AddMemberLoading extends PPEEditState {}

class AddMemberSuccess extends PPEEditState {}

class AddMemberFailure extends PPEEditState {}

class SelfAdditionSuccess extends PPEEditState {}

class ShowAdultRemovalRestrictedSheet extends PPEEditState {}

class ShowSelfRemovalSheet extends PPEEditState {
  final String insuredId;
  ShowSelfRemovalSheet({required this.insuredId});
}
