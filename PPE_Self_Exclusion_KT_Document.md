# PPE Self-Exclusion Feature - Knowledge Transfer Document

## Table of Contents
1. [Overview](#overview)
2. [Architecture](#architecture)
3. [Core Components](#core-components)
4. [Implementation Details](#implementation-details)
5. [State Management](#state-management)
6. [Validation Logic](#validation-logic)
7. [API Integration](#api-integration)
8. [UI Components](#ui-components)
9. [Testing Guidelines](#testing-guidelines)
10. [Troubleshooting](#troubleshooting)

## Overview

The PPE (Pre-Policy Edits) Self-Exclusion feature allows health insurance policy proposers to exclude themselves from coverage while maintaining their role as the policy proposer. This feature includes comprehensive validation, state management, and UI flows for both self-exclusion and self-addition back to the policy.

### Key Features
- **Self-Exclusion**: Proposers can remove themselves from policy coverage
- **Self-Addition**: Self-excluded proposers can add themselves back
- **Validation**: Prevents invalid policy states (no members, only children)
- **State Persistence**: Maintains exclusion state across API calls
- **UI Separation**: Self-excluded proposers appear in separate "Proposer Details" section

### Business Rules
1. At least one member must remain in the policy
2. Cannot leave only children without adults
3. Self-excluded proposers maintain proposer role but lose coverage
4. Self-excluded proposers can edit their details and add themselves back

## Architecture

The feature follows Flutter BLoC pattern with clear separation of concerns:

```
UI Layer (Screens/Widgets)
    ↓
BLoC Layer (Cubit/States)
    ↓
Models Layer (Form Values/Member Details)
    ↓
API Layer (Journey Manager)
```

### Core File Structure
```
lib/feature/endorsement/health/pre_policy_edits/
├── bloc/
│   └── edit_bloc/
│       ├── ppe_edit_bloc.dart              # Main business logic
│       └── ppe_edit_states.dart            # State definitions
├── models/
│   └── ui_models/
│       ├── ppe_form_editing_models.dart    # Form data models
│       └── ppe_self_exclusion_details_model.dart
├── view/
│   ├── screens/
│   │   ├── ppe_edit_screen.dart            # Main edit screen
│   │   └── ppe_self_addition_screen.dart   # Self-addition flow
│   └── bottomsheets/
│       ├── ppe_self_removal_sheet.dart     # Confirmation dialog
│       └── ppe_adult_removal_restricted_sheet.dart
```

## Core Components

### 1. PPE Edit Cubit (`ppe_edit_bloc.dart`)

**Key Properties:**
```dart
class PPEEditCubit extends AckoSafeCubit<PPEEditState> {
  FormValues formValues = FormValues();
  String? selfInsuredId;  // Persists self's insured ID
  bool newSkuPlan = false;
  
  // Lines 25-48
}
```

**Critical Methods:**

#### Member Validation (Lines 516-541)
```dart
bool validateMemberRemoval(String insuredId) {
  // Check minimum member requirement
  int activeMembersCount = _countActiveMembersInPolicy();
  if (activeMembersCount <= 1) {
    emit(MinimumMemberRequiredToast());
    return false;
  }
  
  // Check adult/children constraint
  if (_wouldLeaveOnlyChildren(insuredId)) {
    emit(ShowAdultRemovalRestrictedSheet());
    return false;
  }
  
  // Check if proposer is removing themselves
  if (formValues.newValues?.proposerDetails?.insuredId == insuredId) {
    emit(ShowSelfRemovalSheet(insuredId: insuredId));
    return false;
  }
  
  return true;
}
```

#### Active Member Counting (Lines 496-512)
```dart
int _countActiveMembersInPolicy() {
  int count = 0;
  
  // Count proposer if not self-excluded
  if (formValues.newValues?.proposerDetails?.isSelfExcluded != true) {
    count++;
  }
  
  // Count non-removed members
  formValues.newValues?.memberDetails?.forEach((member) {
    if (member?.isRemoved != true) {
      count++;
    }
  });
  
  return count;
}
```

### 2. Member Details Model (`ppe_form_editing_models.dart`)

**Self-Exclusion Flags (Lines 769-770):**
```dart
class MemberDetails {
  bool isSelfExcluded;    // Current UI state
  bool wasSelfExcluded;   // Historical API state
  
  // Lines 802-803: Default values
  this.isSelfExcluded = false,
  this.wasSelfExcluded = false,
}
```

**State Preservation Logic (Lines 114-143):**
```dart
// CRITICAL FIX: Preserve self-exclusion state absolutely
bool preserveIsSelfExcluded = false;
bool preserveWasSelfExcluded = false;

if (existingProposer != null) {
  // Preserve existing state completely
  preserveIsSelfExcluded = existingProposer.isSelfExcluded;
  preserveWasSelfExcluded = existingProposer.wasSelfExcluded;
} else {
  // Use API-derived state for initial load
  preserveIsSelfExcluded = wasSelfExcluded;
  preserveWasSelfExcluded = wasSelfExcluded;
}
```

### 3. State Definitions (`ppe_edit_states.dart`)

**Self-Exclusion States:**
```dart
class MinimumMemberRequiredToast extends PPEEditState {}  // Line 58

class SelfAdditionSuccess extends PPEEditState {}  // Line 80

class ShowAdultRemovalRestrictedSheet extends PPEEditState {}  // Line 82

class ShowSelfRemovalSheet extends PPEEditState {  // Lines 84-86
  final String insuredId;
  ShowSelfRemovalSheet({required this.insuredId});
}
```

## Implementation Details

### Self-Exclusion Flow

1. **User Clicks Remove on Proposer**
   - `validateMemberRemoval()` called (Line 516)
   - Emits `ShowSelfRemovalSheet` state
   - UI shows confirmation bottom sheet

2. **User Confirms Removal**
   - `proceedWithMemberRemoval()` called (Line 544)
   - Sets `isSelfExcluded = true` on proposer
   - Proposer moves to "Proposer Details" section
   - Member details section hides proposer

3. **State Persistence**
   - `selfInsuredId` stored in cubit (Line 41)
   - Flags preserved across API calls
   - Form validation skips self-excluded proposers

### Self-Addition Flow

1. **User Clicks "Add me back"**
   - Navigates to `PpeSelfAdditionScreen`
   - Collects height and weight only

2. **Form Submission**
   - `addProposerSelfToPolicy()` called
   - Reuses existing proposer data
   - Sets `isSelfExcluded = false`
   - Emits `SelfAdditionSuccess` state

3. **UI Update**
   - Proposer appears in member details
   - Proposer details section hidden
   - Continue button enabled

### Edit Counting Logic (Lines 696-709)

```dart
// Count proposer self-exclusion/inclusion changes
bool isProposerSelfExclusion = 
    newMember.isSelfExcluded && !oldMember.wasSelfExcluded;

bool isProposerSelfInclusion = 
    !newMember.isSelfExcluded && oldMember.wasSelfExcluded;

bool isRegularRemoval = newMember.isRemoved && !newMember.isSelfExcluded;

if (isProposerSelfExclusion || isProposerSelfInclusion || isRegularRemoval) {
  edits++;
}
```

## State Management

### BLoC States Flow

```
Initial → Loading → Loaded
    ↓
ValidationCheck
    ├── MinimumMemberRequiredToast
    ├── ShowAdultRemovalRestrictedSheet  
    ├── ShowSelfRemovalSheet
    └── DirectRemoval
```

### Form State Synchronization

The feature maintains two parallel data structures:
- **oldValues**: Original API data (immutable)
- **newValues**: Current form state (mutable)

**Critical Synchronization Points:**
1. Initial load (`getEditsData()` - Line 52)
2. API calls (`syncApiAndFormValues()` - Line 341)
3. Form updates (`findAndUpdateMemberDetails()`)

## Validation Logic

### Three-Tier Validation System

#### 1. Minimum Member Validation
```dart
if (activeMembersCount <= 1) {
  emit(MinimumMemberRequiredToast());
  return false;
}
```
**Toast Message**: "At least one member required in the policy"

#### 2. Adult/Children Constraint
```dart
if (_wouldLeaveOnlyChildren(insuredId)) {
  emit(ShowAdultRemovalRestrictedSheet());
  return false;
}
```
**Prevents**: Removing last adult, leaving only children

#### 3. Self-Removal Confirmation
```dart
if (formValues.newValues?.proposerDetails?.insuredId == insuredId) {
  emit(ShowSelfRemovalSheet(insuredId: insuredId));
  return false;
}
```
**Shows**: Confirmation dialog for proposer self-removal

### Validation Helper Methods

#### `_wouldLeaveOnlyChildren()` (Lines 425-493)
- Counts remaining adults after removal
- Checks relationship types
- Returns true if only children would remain

#### `_countActiveMembersInPolicy()` (Lines 496-512)
- Counts non-removed members
- Excludes self-excluded proposer
- Used for minimum member validation

## API Integration

### Journey Manager Nodes

1. **HYDRATE_DETAILS**: Initial data load
2. **PREMIUM**: Premium calculation after changes
3. **EDIT**: Final submission

### Request Body Structure
```dart
Map<String, dynamic> requestBody = {
  "current_node_id": PrePolicyEditNodes.EDIT.value,
  "edit": {
    "entity_id": proposalId,
    "journey": "health_pre_policy_edit", 
    "entity_type": "proposal"
  },
  "input_data_id": null
};
```

### API Response Handling

**Self-Exclusion Detection:**
```dart
bool wasSelfExcluded = insuredUsers?.insuredMap.values
    .where((insured) => insured.parameters?.parameterMap['user_id']?.value == 
           proposerDetails?.usersMap.values.firstOrNull?.userId)
    .isEmpty ?? true;
```

**Data Merging:**
- Proposer data from `usersContainer`
- Coverage data from `insuredContainer`  
- Height/weight merged when available

## UI Components

### Main Edit Screen (`ppe_edit_screen.dart`)

#### Conditional Rendering (Lines 344-411)
```dart
if (selfExcluded) ...[
  // Proposer Details Section
  CustomExpansionTile(
    title: 'Proposer details',
    content: MemberDetailsForm(
      selfExclusionDetails: PpeSelfExclusionDetailsModel(
        memberTitle: 'Self',
        note: 'Note: Your policy will be issued in this name, but you will not be covered under it.',
        removal: false
      ),
      // ... proposer form fields
    ),
  ),
]
```

#### State Listeners (Lines 180-211)
```dart
if (state is MinimumMemberRequiredToast) {
  _healthJMUtil.showToastMessage(
    message: "At least one member required in the policy"
  );
}

if (state is ShowSelfRemovalSheet) {
  context.showAckoModalBottomSheet(
    child: PpeSelfRemovalSheet(
      onPrimaryTap: () {
        Navigator.pop(context);
        _cubit?.proceedWithMemberRemoval(state.insuredId);
      },
      onSecondaryTap: () => Navigator.pop(context),
    )
  );
}
```

### Self Addition Screen (`ppe_self_addition_screen.dart`)

**Simple Form (Lines 86-94):**
```dart
AckoTextFormField().heightInputField(
  controller: _heightEditingController,
  placeholder: height,
  onChanged: (value) => setState(() {})
),

AckoTextFormField().weightInputField(
  controller: _weightController,
  placeholder: weight,
  onChanged: (value) => setState(() {})
),
```

**Form Validation (Lines 102-105):**
```dart
bool _isFormValid() {
  return _heightEditingController.text.isNotEmpty &&
         _weightController.text.isNotEmpty;
}
```

### Bottom Sheets

#### Self Removal Sheet (`ppe_self_removal_sheet.dart`)
- **Purpose**: Confirm proposer self-exclusion
- **Actions**: Primary (Remove), Secondary (Cancel)
- **Content**: Dynamic from remote config

#### Adult Removal Restricted Sheet (`ppe_adult_removal_restricted_sheet.dart`)  
- **Purpose**: Prevent invalid family structure
- **Action**: Single "Got it" button
- **Trigger**: When removing last adult

## Testing Guidelines

### Unit Tests

#### Validation Logic Tests
```dart
test('should prevent removal when only one member left', () {
  // Setup: Policy with single member
  // Action: Call validateMemberRemoval()
  // Assert: Returns false, emits MinimumMemberRequiredToast
});

test('should prevent removal leaving only children', () {
  // Setup: Policy with one adult, multiple children
  // Action: Remove adult
  // Assert: Returns false, emits ShowAdultRemovalRestrictedSheet
});
```

#### State Management Tests
```dart
test('should preserve self-exclusion state across API calls', () {
  // Setup: Self-excluded proposer
  // Action: Trigger API sync
  // Assert: isSelfExcluded remains true
});
```

### Integration Tests

#### Self-Exclusion Flow
1. Load policy with proposer
2. Click remove on proposer
3. Confirm removal in bottom sheet
4. Verify proposer appears in proposer details section
5. Verify member details section excludes proposer

#### Self-Addition Flow  
1. Start with self-excluded proposer
2. Click "Add me back"
3. Fill height/weight form
4. Submit form
5. Verify proposer appears in member details
6. Verify proposer details section hidden

### Edge Cases to Test

1. **API Failures**: Network errors during self-exclusion
2. **State Corruption**: Invalid flag combinations
3. **Concurrent Edits**: Multiple users editing same policy
4. **Data Inconsistency**: Mismatched user/insured data

## Troubleshooting

### Common Issues

#### 1. Proposer Not Appearing After Self-Addition
**Symptoms**: Self-addition succeeds but proposer still in proposer details
**Cause**: `isSelfExcluded` flag not properly reset
**Fix**: Check `addProposerSelfToPolicy()` method, ensure flag update

#### 2. Validation Not Triggering
**Symptoms**: Can remove last member or leave only children
**Cause**: `validateMemberRemoval()` not called or logic error
**Fix**: Verify method call in `removeMember()`, check counting logic

#### 3. State Not Persisting
**Symptoms**: Self-exclusion state lost after API calls
**Cause**: State not preserved in form synchronization
**Fix**: Check `populateFormValuesFromPrePolicyEditsResponse()` method

#### 4. Edit Count Incorrect
**Symptoms**: Continue button disabled despite changes
**Cause**: Self-exclusion changes not counted properly
**Fix**: Verify edit counting logic in `_countFieldChanges()`

### Debug Tools

#### Logging Self-Exclusion State
```dart
print('Proposer State: isSelfExcluded=${proposer.isSelfExcluded}, wasSelfExcluded=${proposer.wasSelfExcluded}');
print('Active Members Count: ${_countActiveMembersInPolicy()}');
```

#### State Inspection
```dart
// In PPEEditCubit
void debugPrintState() {
  print('FormValues: ${formValues.toJson()}');
  print('SelfInsuredId: $selfInsuredId');
  print('EditType: $editType');
}
```

### Performance Considerations

1. **State Preservation**: Minimize API calls by preserving state locally
2. **Validation Caching**: Cache validation results for repeated checks
3. **UI Updates**: Use selective rebuilds for large member lists
4. **Memory Management**: Dispose controllers and listeners properly

## Commit History Analysis

### Feature Development Timeline

The PPE Self-Exclusion feature was developed through 12 commits spanning from June 17 to July 3, 2025:

#### Phase 1: Initial Implementation (June 17-23)
1. **d8f55f4ec** - "feat: self exclusion ui" (399 additions, 79 deletions)
   - Added bottom sheets for self-removal and adult removal restriction
   - Updated endorsement copies and health JM response models
   - **Files**: 6 files modified

2. **c17388fe7** - "ui: self addition" (82 additions)
   - Created `ppe_self_addition_screen.dart`
   - Basic UI for height/weight input form
   - **Files**: 1 file added

3. **e40f5d177** - "constants" (15 additions, 7 deletions)
   - Updated health JM constants
   - Refined self-addition screen constants
   - **Files**: 2 files modified

#### Phase 2: Core Feature Implementation (June 23)
4. **dd199ecd0** - "Enables self-exclusion/addition for health policies" (722 additions, 268 deletions)
   - **Major milestone**: Complete self-exclusion/addition functionality
   - Added self-exclusion details model
   - Updated policy overview and review changes screens
   - Enhanced form editing models with self-exclusion logic
   - **Files**: 14 files modified
   - **Key additions**:
     - `ppe_self_exclusion_details_model.dart`
     - Self-addition screen enhancements
     - Review changes integration

#### Phase 3: Bug Fixes and Refinements (June 26-July 1)
5. **b1be8f417** - "ppe self exclusion" (652 additions, 155 deletions)
   - **Critical fixes**: Proposer removal prevention
   - Self-exclusion state maintenance during edits
   - Adult/children validation logic
   - Continue button logic improvements
   - **Files**: 8 files modified

6. **9dabff5b6** - "fix: ppe self exclusion" (9 additions, 8 deletions)
   - Minor fixes in form editing models
   - **Files**: 1 file modified

7. **3005bb395** - "Fixes proposer restoration after self-exclusion" (45 additions, 44 deletions)
   - Improved proposer ID storage and reuse
   - Height/weight update logic
   - **Files**: 1 file modified (`ppe_edit_bloc.dart`)

8. **c4dc4bdb2** - "Fixes self-insured ID handling in PPE flow" (50 additions, 9 deletions)
   - Enhanced self-insured ID extraction and storage
   - Prevents data inconsistencies
   - **Files**: 1 file modified (`ppe_edit_bloc.dart`)

#### Phase 4: Logic Refinements (July 1-2)
9. **818a7ef5e** - "Fixes proposer self-exclusion/inclusion logic" (144 additions, 116 deletions)
   - **Major refactor**: Edit counting and state tracking
   - Improved transition detection (`wasSelfExcluded` → `isSelfExcluded`)
   - Enhanced UI state reflection
   - **Files**: 4 files modified

10. **e2d97a226** - "Fixes PPE form editing logic" (49 additions, 18 deletions)
    - Corrected proposer information determination
    - Fixed field change counting for self-excluded proposers
    - **Files**: 2 files modified

#### Phase 5: Final Validations (July 3)
11. **76cc06c61** - "new sku" (2 additions, 3 deletions)
    - Minor SKU-related updates
    - **Files**: 2 files modified

12. **0f9957b2a** - "Prevents removal of the last policy member" (34 additions)
    - **Final validation**: Minimum member requirement
    - Added `MinimumMemberRequiredToast` state
    - Comprehensive member counting logic
    - **Files**: 3 files modified

### Code Metrics Summary

| Phase | Commits | Total Additions | Total Deletions | Files Modified |
|-------|---------|----------------|-----------------|----------------|
| Phase 1 | 3 | 496 | 86 | 9 |
| Phase 2 | 1 | 722 | 268 | 14 |
| Phase 3 | 3 | 706 | 207 | 10 |
| Phase 4 | 2 | 193 | 134 | 6 |
| Phase 5 | 2 | 36 | 3 | 5 |
| **Total** | **12** | **2,153** | **698** | **44** |

### Key Technical Decisions

#### 1. State Management Strategy
- **Decision**: Use dual flags (`isSelfExcluded`, `wasSelfExcluded`)
- **Rationale**: Distinguish between current UI state and historical API state
- **Implementation**: Lines 769-770 in `ppe_form_editing_models.dart`

#### 2. Validation Architecture
- **Decision**: Three-tier validation system
- **Rationale**: Handle different business rules with appropriate UI feedback
- **Implementation**: `validateMemberRemoval()` method (Lines 516-541)

#### 3. ID Persistence Strategy
- **Decision**: Store `selfInsuredId` at cubit level
- **Rationale**: Maintain consistency across add/remove operations
- **Implementation**: Line 41 in `ppe_edit_bloc.dart`

#### 4. UI Separation Pattern
- **Decision**: Separate "Proposer Details" and "Member Details" sections
- **Rationale**: Clear visual distinction for self-excluded proposers
- **Implementation**: Conditional rendering (Lines 344-411 in `ppe_edit_screen.dart`)

## Advanced Implementation Details

### Critical Code Sections

#### Self-Exclusion State Preservation (Lines 114-143 in `ppe_form_editing_models.dart`)
```dart
// CRITICAL FIX: Preserve self-exclusion state absolutely
// Once a proposer is self-excluded, they stay self-excluded until explicitly added back
bool preserveIsSelfExcluded = false;
bool preserveWasSelfExcluded = false;

if (existingProposer != null) {
  // If we have existing proposer state, preserve it completely
  preserveIsSelfExcluded = existingProposer.isSelfExcluded;
  preserveWasSelfExcluded = existingProposer.wasSelfExcluded;
} else {
  // FIXED: For API rebuilds, be more conservative about marking proposer as self-excluded
  // Only mark as self-excluded if this is the initial load (oldValues) or if explicitly set
  if (isOld) {
    // For oldValues, use API-derived state
    preserveIsSelfExcluded = wasSelfExcluded;
    preserveWasSelfExcluded = wasSelfExcluded;
  } else {
    // For newValues, use API-derived state to reflect current reality
    // If API shows proposer is not in insured container, they are self-excluded
    preserveIsSelfExcluded = wasSelfExcluded;
    preserveWasSelfExcluded = wasSelfExcluded;
  }
}
```

#### Adult/Children Validation Logic (Lines 425-493 in `ppe_edit_bloc.dart`)
```dart
bool _wouldLeaveOnlyChildren(String insuredIdToRemove) {
  // Get all current members (excluding the one being removed)
  List<MemberDetails?> remainingMembers = [];

  // Add proposer if not being removed and not self-excluded
  if (formValues.newValues?.proposerDetails?.insuredId != insuredIdToRemove &&
      formValues.newValues?.proposerDetails?.isSelfExcluded != true) {
    remainingMembers.add(formValues.newValues?.proposerDetails);
  }

  // Add other members (excluding the one being removed)
  formValues.newValues?.memberDetails?.forEach((member) {
    if (member?.insuredId != insuredIdToRemove && member?.isRemoved != true) {
      remainingMembers.add(member);
    }
  });

  // Check if any remaining member is an adult
  for (var member in remainingMembers) {
    if (member != null) {
      String relation = member.relation?.toLowerCase() ?? '';
      // Adults: self, spouse, father, mother, father-in-law, mother-in-law
      if (relation == 'self' ||
          relation == 'spouse' ||
          relation == 'father' ||
          relation == 'mother' ||
          relation == 'father-in-law' ||
          relation == 'mother-in-law') {
        return false; // At least one adult will remain
      }
    }
  }

  return true; // Only children would remain
}
```

#### Edit Counting for Self-Exclusion (Lines 696-709 in `ppe_form_editing_models.dart`)
```dart
// CRITICAL FIX: Count proposer self-exclusion/inclusion changes correctly
// Case 1: Proposer becomes self-excluded (removal)
bool isProposerSelfExclusion =
    newMember.isSelfExcluded && !oldMember.wasSelfExcluded;

// Case 2: Proposer was self-excluded but is now being added back (inclusion)
bool isProposerSelfInclusion =
    !newMember.isSelfExcluded && oldMember.wasSelfExcluded;

// Case 3: Regular member removal (non-proposer)
bool isRegularRemoval = newMember.isRemoved && !newMember.isSelfExcluded;

if (isProposerSelfExclusion ||
    isProposerSelfInclusion ||
    isRegularRemoval) {
  edits++;
}
```

### Performance Optimizations

#### 1. Selective State Updates
- Only rebuild affected UI sections when self-exclusion state changes
- Use `BlocBuilder` with specific state conditions
- Minimize full form re-renders

#### 2. Validation Caching
- Cache validation results to avoid repeated calculations
- Use memoization for expensive operations like member counting
- Debounce validation calls during rapid user interactions

#### 3. Memory Management
- Proper disposal of controllers in `PpeSelfAdditionScreen`
- Clear temporary state after successful operations
- Avoid memory leaks in long-running edit sessions

### Error Handling Strategies

#### 1. API Failure Recovery
```dart
// In syncApiAndFormValues method
try {
  final response = await _repo.manageJourney(JourneyType.PRE_POLICY_EDIT, requestBody);
  // Handle success
} catch (error) {
  // Preserve local state, show error toast
  emit(ErrorToast());
  // Don't reset self-exclusion flags on API failure
}
```

#### 2. State Corruption Prevention
```dart
// Validate state consistency before API calls
bool isStateValid() {
  final proposer = formValues.newValues?.proposerDetails;
  if (proposer == null) return false;

  // Ensure flag consistency
  if (proposer.isSelfExcluded && !proposer.wasSelfExcluded) {
    // This indicates a new self-exclusion
    return true;
  }

  if (!proposer.isSelfExcluded && proposer.wasSelfExcluded) {
    // This indicates self-addition back
    return true;
  }

  return proposer.isSelfExcluded == proposer.wasSelfExcluded;
}
```

#### 3. UI Consistency Checks
```dart
// Ensure UI reflects actual state
void validateUIConsistency() {
  final proposer = formValues.newValues?.proposerDetails;
  final selfExcluded = proposer?.isSelfExcluded ?? false;

  // Proposer should appear in correct section
  assert(selfExcluded ?
    proposerInProposerSection :
    proposerInMemberSection);
}
```

---

**Document Version**: 1.0
**Last Updated**: July 4, 2025
**Author**: Development Team
**Review Status**: Ready for Publication
